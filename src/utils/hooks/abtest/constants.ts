import { AccessFineLocationModel } from '@/utils/hooks/abtest/types';

export enum PullSuccessEnum {
  DEFAULT = -1,
  SUCCESS = 0,
  ERROR = 1
}

// 定位策略
export enum GuideLocEnum {
  TACTICS_DIM = 'a',
  TACTICS_PRECISE = 'b'
}

// abtest key
export enum AbtestEnum {
  // 是否开启二次定位数据更新
  // GET_LOCATION_BY_NEW_USER_LEAD = 'GetLocationByNewUserLead',
  // 读取手机号码推荐配置键是否命中，是否开启电话引导
  // NEW_USER_PHONE = 'newUser_phone',
  // 是否禁止一键登录
  // QUICKLY_LOGIN_FORBIDEN = 'quicklyLgoin_Forbiden',
  QUICKLY_LOGIN_FORBIDEN = 'quicklyLgoin-Forbiden',
  // 是否需要二次确认
  // RECONFIRM_DIALOD = 'reconfirm_dialog',
  RECONFIRM_DIALOD = 'reconfirm-dialog',
  // 摩托配置 todo 可删除
  MOTO_GUIDE = 'moto_guide',
  // 1v1推荐驾校
  // NEW_USER_1V1 = 'newUser1v1',
  NEW_USER_1V1 = 'newUser-1v1',
  // 留资结果
  // NEW_USER_DIALOG = 'newuser_dialog',
  NEW_USER_DIALOG = 'newuser-dialog',
  // 问卷页面提示是否展示
  INFORMATION_COLLECT_CANCEL_TIP = 'informationCollectCancelTip',
  // 定位策略key
  JK_GUIDE_LOC = 'jk-guide-loc'
}

// 默认模糊定位
export const DEFAULT_GUIDE_LOC: AccessFineLocationModel = {
  key: AbtestEnum.JK_GUIDE_LOC,
  strategy: GuideLocEnum.TACTICS_DIM
};
