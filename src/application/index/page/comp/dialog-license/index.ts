import { defineComponent, reactive, toRefs } from 'vue';
import { ItemModel, StateModel } from './types';
import Deferred, { DeferredModel } from '@/utils/deferred';

const LicenseList: ItemModel[] = [
  {
    carType: 'C1',
    carDesc: '小型汽车'
  },
  {
    carType: 'D',
    carDesc: '摩托车'
  }
];

export default defineComponent({
  setup() {
    let dtd: DeferredModel<ItemModel> = null;

    const state = reactive<StateModel>({
      visible: false // 是否显示弹框
    });

    const methods = {
      // 显示弹框
      show() {
        state.visible = true;

        dtd = Deferred();

        return dtd.promise;
      },
      // 关闭弹框
      onClose() {
        dtd.reject('手动取消');

        methods.close();
      },
      // 选择驾照类型
      chooseLicense(license: ItemModel) {
        dtd.resolve(license);

        methods.close();
      },
      close() {
        state.visible = false;

        methods.reset();
      },
      reset() {
        dtd = null;
      }
    };

    return {
      ...toRefs(state),
      LicenseList,
      ...methods
    };
  }
});
