// Vue 3 类型声明 - 强制确保所有 .vue 文件都被识别为模块
declare module '*.vue' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<any, any, any>;
  export default component;
}

// 明确声明特定的 Vue 文件路径
declare module '@/application/index/page/comp/dialog-license/index.vue' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<any, any, any>;
  export default component;
}

// 绝对路径声明
declare module '/Users/<USER>/Documents/7work/mucang/h5/jiakaobaodian-beginner-guidance/src/application/index/page/comp/dialog-license/index.vue' {
  import { DefineComponent } from 'vue';
  const component: DefineComponent<any, any, any>;
  export default component;
}

// 确保 Vue 模块的类型可用
declare module 'vue' {
  export * from '@vue/runtime-dom';
  export * from '@vue/runtime-core';
  export * from '@vue/reactivity';
  export * from '@vue/shared';

  // 主要的 Vue 3 API
  export function defineComponent<T>(options: T): T;
  export function reactive<T extends object>(target: T): T;
  export function ref<T = any>(value?: T): { value: T };
  export function computed<T>(getter: () => T): { value: T };
  export function toRefs<T extends object>(object: T): any;
  export function onMounted(fn: () => void): void;
  export function onUnmounted(fn: () => void): void;
  export function watch<T>(source: () => T, callback: (newVal: T, oldVal: T) => void): void;
  export function watch<T>(source: T, callback: (newVal: any, oldVal: any) => void): void;

  // PropType
  export interface PropType<T> {
    new (...args: any[]): T & {};
    __propType?: T;
  }
}

declare module '@vue/runtime-core' {
  export interface ComponentCustomProperties {
    // 这里可以添加全局属性的类型声明
  }
}

declare module 'window' {
  export default any;
}

declare interface Window {
  MCProtocolIosExecute: any;
  getMucangIOSWebViewData: any;
  mucang: any;
  Package: any;
}
