import { defineComponent, reactive, ref, toRefs } from 'vue';
import VerifyPhoneSliderComp from '@/application/index/page/comp/verify-phone-slider/index.vue';
import VerifyPhoneCodeComp from '@/application/index/page/comp/verify-phone-code/index.vue';
import Deferred, { DeferredModel } from '@/utils/deferred';
import { LoginResponse } from '@/application/index/page/comp/verify-phone-code/types';

export default defineComponent({
  components: {
    VerifyPhoneSliderComp,
    VerifyPhoneCodeComp
  },
  setup() {
    let dtd: DeferredModel<LoginResponse> = null;
    let currentPhone: string;

    const state = reactive({});

    const constants = {};

    const components = {
      sliderRef: ref<InstanceType<typeof VerifyPhoneSliderComp>>(null),
      codeRef: ref<InstanceType<typeof VerifyPhoneCodeComp>>(null)
    };

    const methods = {
      open(phone: string) {
        currentPhone = phone;
        components.sliderRef.value.show();

        dtd = Deferred();

        return dtd.promise;
      },
      onEnd(item: LoginResponse) {
        dtd.resolve(item);

        methods.close();
      },
      onSuccess(token: string) {
        components.codeRef.value.show(currentPhone, token);
      },
      onBack() {
        components.sliderRef.value.show();
      },
      close() {
        methods.reset();
      },
      reset() {
        dtd = null;
      }
    };

    return {
      ...toRefs(state),
      ...constants,
      ...components,
      ...methods
    };
  }
});
