import MCProtocol from '@simplex/simple-mcprotocol';
import Utils from '@/utils/utils';
import { JumpQueryModel } from '@/utils/types';

export default function () {
  return {
    // 页面跳转
    goPathJumps(query: JumpQueryModel): void {
      if (Utils.isInApp) {
        this.inAppPathJumps(query);
      } else {
        this.outSideAppPathJumps(query);
      }
    },
    // app内跳转
    inAppPathJumps({ path, coreOpenParams, webQuery }: JumpQueryModel) {
      const url = APP.domain.mc + '/' + path + '?' + Utils.param(webQuery);
      // console.log(url);
      MCProtocol.Core.Web.open({
        ...coreOpenParams,
        url
      });
    },
    // app外跳转
    outSideAppPathJumps({ path, webQuery, type = 'href' }: JumpQueryModel) {
      const url = path + '?' + Utils.param(webQuery);
      switch (type) {
        case 'href':
          location.href = url;
          break;
        case 'replace':
          location.replace(url);
          break;
        default:
      }
    }
  };
}
