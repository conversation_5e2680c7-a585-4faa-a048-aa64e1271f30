import { defineComponent, reactive, toRefs } from 'vue';
import UI from '@/utils/ui';
import { PostLoginStore, PostSmsCheckStore } from '@/store/auth/login-sms';
import { EmitModel, LoginResponse, StateModel } from './types';
import { oortToast } from '@/application/index/page/comp/form/utils';
import { getBaseParams } from '@/application/index/page/comp/verify-phone-code/constants';
import { ToastEnum } from '@/application/index/page/comp/form/constants';

export default defineComponent({
  setup(props, { emit }: { emit: EmitModel }) {
    // 倒计时定时器
    let timer = null;

    const state = reactive(<StateModel>{
      // 是否显示弹框
      visible: false,
      // 验证码
      code: '',
      // 倒计时
      countdown: 0,
      // 手机号
      phone: '',
      // 网易验证码返回的code
      validate: '',
      // 获取验证码的code
      smsId: ''
    });

    const methods = {
      // 显示验证码弹框
      show(phone: string, validate: string) {
        state.visible = true;
        state.phone = phone;
        state.validate = validate;
        methods.sendSms();
      },
      // 关闭弹框
      onClose() {
        state.visible = false;

        if (timer) {
          clearInterval(timer);
        }
      },
      // 重新打开图形验证码弹框
      onBack() {
        methods.onClose();
        emit('back', state.phone);
      },
      // 发送验证码
      sendSms() {
        PostSmsCheckStore({
          ...getBaseParams(),
          NECaptchaValidate: state.validate,
          phoneNumber: state.phone
        })
          .then(res => {
            state.smsId = (res as any).smsId;
            methods.startCountdown();
          })
          .catch(err => {
            UI.toast(err.message);
          });
      },
      // 倒计时
      startCountdown() {
        state.countdown = 60;
        timer = setInterval(() => {
          state.countdown--;
          if (state.countdown === 0) {
            clearInterval(timer);
          }
        }, 1000);
      },
      // 提交
      onSubmit() {
        if (!state.smsId) {
          oortToast(ToastEnum.SEND_SMS);
          UI.toast('请先发送验证码');
          return;
        }
        if (!state.code) {
          oortToast(ToastEnum.FILL_CODE);
          UI.toast('请填写验证码');
          return;
        }
        PostLoginStore({
          ...getBaseParams(),
          phoneNumber: state.phone,
          smsCode: state.code,
          smsId: state.smsId
        })
          .then((res: LoginResponse) => {
            emit('success', {
              authToken: res.authToken,
              mucangId: res.mucangId
            });

            methods.onClose();
          })
          .catch(err => {
            UI.toast(err.message);
          });
      }
    };

    return {
      ...toRefs(state),
      ...methods
    };
  }
});
