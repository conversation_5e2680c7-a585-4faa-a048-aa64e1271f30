import Utils from '@/utils/utils';
import { useRemoteSettingStore } from '@/utils/hooks/abtest';
import MCProtocol from '@simplex/simple-mcprotocol';
import { ACCESS_FINE_LOCATION_STATUS } from '@/utils/constant';
import { NEW_USER_LEAD_BOOL } from '@/utils/storage-key';
import { trackEvent } from '@/utils/stat';
import { GuideLocEnum } from '@/utils/hooks/abtest/constants';
import { LocationModel, ShowLocationModel } from '@/utils/hooks/location/types';

export const useLocationStore = () => {
  const remoteSettingStore = useRemoteSettingStore();

  const methods = {
    async getLocation(): Promise<LocationModel> {
      const systemBaseInfo = await Utils.getSystemBaseInfo();
      console.log('systemBaseInfo: ', systemBaseInfo);

      // ios，直接返回基础信息
      if (!Utils.isAndroid) {
        return systemBaseInfo;
      }

      // 安卓
      methods.postStrategyOort();
      const { _longitude, _latitude } = systemBaseInfo;

      // 判断版本号
      if (systemBaseInfo._systemVersion < 12) {
        // 获取定位策略
        const { strategy } = await remoteSettingStore.getLocationStrategyKey();

        // 基础信息有经纬度，直接返回基础信息
        if (_longitude && _latitude) {
          return systemBaseInfo;
        }

        // a策略
        if (strategy === GuideLocEnum.TACTICS_DIM) {
          // 基础信息没有经纬度，弹窗申请权限并返回经纬度
          return await methods.showRequestDialog();
        }

        // b策略，不做任何处理
        return null;
      }

      // 无经纬度，不做任何处理
      if (!_longitude || !_latitude) {
        return null;
      }

      // 获取是否开启精准定位
      const hasAccuratePosition = await Utils.checkAccurateControl();

      // 无精准定位，开启二次定位数据更新
      if (hasAccuratePosition.message === ACCESS_FINE_LOCATION_STATUS.fail) {
        Utils.store.set(NEW_USER_LEAD_BOOL, true);
      }

      return systemBaseInfo;
    },
    // 调取请求授权弹窗
    showRequestDialog(): Promise<ShowLocationModel> {
      return new Promise((resolve, reject) => {
        MCProtocol.jxxy.show.location({
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          manual: false,
          callback: res => {
            // console.log(res);
            if (res) {
              const lat = typeof res === 'string' ? JSON.parse(res) : res;
              if (lat.data) {
                reject();
              } else {
                // 前端组装数据,返回值跟system.info 数据保持格式一致
                const locationModelData = {
                  _latitude: res.latitude,
                  _longitude: res.longitude,
                  _userCity: res.cityCode
                };
                resolve(locationModelData);
              }
            } else {
              reject();
            }
          }
        });
      });
    },
    // 定位策略埋点数据
    postStrategyOort() {
      Promise.all([
        remoteSettingStore.getLocationStrategyKey(),
        Utils.getSystemBaseInfo(),
        Utils.checkAccurateControl()
      ]).then(([{ strategy }, { _longitude }, hasAccuratePosition]) => {
        trackEvent({
          PageName: '帮我找驾校页h5',
          actionName: '定位权限',
          actionType: '',
          // true获取到经纬度，false获取到经纬度
          hasLocation: !!_longitude,
          // true同意授权，false不同意授权
          hasAccurate: hasAccuratePosition.message === ACCESS_FINE_LOCATION_STATUS.success
        });
        trackEvent({
          PageName: '帮我找驾校页h5',
          actionName: '定位策略',
          actionType: '',
          locationStrategy: strategy // 定位策略
        });
      });
    }
  };

  return {
    ...methods
  };
};
