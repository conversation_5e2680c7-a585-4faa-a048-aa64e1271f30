import { trackEvent } from '@/utils/stat';
import { ToastEnum } from '@/application/index/page/comp/form/constants';
import MCProtocol from '@simplex/simple-mcprotocol';
import Utils from '@/utils/utils';
import { BaseParamsModel, PostDataModel } from '@/application/index/page/comp/form/types';
import { UAV_LICENSE_LIST } from '@/utils/options/license';
import { PostAdd2Store, PostAddPartStore, PostAddUavStore } from '@/store/lead/h5';
import { HANDLE_CHOOSE_LAT_BOOL, NEW_USER_LEAD_BOOL } from '@/utils/storage-key';

// 表单校验的埋点
export const oortToast = (type: ToastEnum) => {
  trackEvent({
    PageName: '帮我找驾校页h5',
    actionName: 'toast显示类型',
    actionType: '',
    numbers: {
      toastType: type
    }
  });
};

// 留资错误上报
export const saveErrorReq = (url: string, params: PostDataModel): Promise<void> => {
  return new Promise(resolve => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    MCProtocol['jiakao-global'].saveErrorReq({
      reqConfig: JSON.stringify({
        host: APP.domain.lead,
        signKey: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9',
        path: url,
        // ios需要手动添加参数进行加密，安卓默认已进行加密
        params: Utils.isIOS
          ? JSON.stringify({
              ...params,
              needEncrypted: true
            })
          : JSON.stringify(params),
        expireTime: new Date().getTime() + 180 * 24 * 60 * 60 * 1000
      }),
      callback: function (data) {
        console.log('saveErrorReq callback: ', data);
        resolve();
      }
    });
  });
};

export const getSubmitMethod = (params: BaseParamsModel) => {
  // 1. locationByNewUserLead 已拉取
  const getNewUserLeadBool = Utils.store.get(NEW_USER_LEAD_BOOL);
  // 2. 沒有手动选择地址
  const getHandleChooseLatBool = Utils.store.get(HANDLE_CHOOSE_LAT_BOOL);

  // locationByNewUserLead 已拉取 代表当前是模糊定位
  // 同时没有手动选取精确地址
  // 所以使用分步留资的接口保存留资数据，让客户端二次上报
  const isStepSubmit = getNewUserLeadBool && !getHandleChooseLatBool;

  if (UAV_LICENSE_LIST.includes(params.userLicenseType)) {
    // 无人机类型增加字段
    const [droneType, licenseType] = params.userLicenseType.split('__');
    params.userLicenseType = droneType;
    params.userLicenseTypeExt = 'S';
    params.userLicenseTypeExt2 = licenseType;

    return {
      fn: PostAddUavStore,
      url: '/api/open/lead/uav-add.htm',
      // 无人机没有分步提交
      isStepSubmit: false
    };
  }

  return {
    fn: isStepSubmit ? PostAddPartStore : PostAdd2Store,
    url: isStepSubmit ? '/api/open/lead/add-part.htm' : '/api/open/lead/add.htm',
    isStepSubmit
  };
};
