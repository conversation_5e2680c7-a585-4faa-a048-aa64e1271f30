export interface StateModel {
  // 选中的值
  selectList: SelectModel[];
}

export interface SelectModel {
  questionCode: number;
  optionIndexList: number[];
}

export interface QuestionItemModel {
  answerNum: number;
  code: number;
  content: string;
  enable: boolean;
  multiple: boolean;
  optionList: OptionItemModel[];
  type: number;
}

export interface OptionItemModel {
  enable: boolean;
  index: number;
  label: string;
  value: string;
}

export interface EmitModel {
  (e: 'change', selectList: SelectModel[]): void;
}
