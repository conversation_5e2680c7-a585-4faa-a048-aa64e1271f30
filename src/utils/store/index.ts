import { MethodEnum } from '@/utils/store/type';
import Utils from '@/utils/utils';
import MCProtocol from '@simplex/simple-mcprotocol';
import { MCProtocolDataModel } from '@simplex/simple-mcprotocol/typings/type';

const HTTP = MCProtocol.Core.Http;
const HTTP2 = MCProtocol.Core.Http2;
// 是否新版本协议，有新特性：htt2.get 或 http2.post
const hasNewFeature = Utils.hasMCFeature('core.http2.get') && Utils.hasMCFeature('core.http2.post');
// const hasNewFeature = false;

export const request = ({
  url,
  method = MethodEnum.GET,
  params,
  options = {}
}: {
  url: string;
  method: MethodEnum;
  params: string | Record<string, any>;
  options?: Record<string, any>;
}) => {
  const httpHead = options.headers || {};
  httpHead.RqFrm = hasNewFeature ? 'req-inj-2' : 'req-inj';

  const initSign = options.sign || '';
  const baseParams = {
    _s: Utils.getRandomR(1),
    _clv: 1024
  };

  if (typeof options.basicParams === 'undefined') {
    options.basicParams = true;
  }

  const _url = url + (url.match(/\?/gi) ? '&' : '?') + Utils.param(baseParams);
  const _params = typeof params === 'string' ? Utils.split(params, '&', '=', true) : params;

  const opt1: Record<string, any> = {
    url: _url,
    params: _params,
    originalData: true,
    // 老版本webview4.0参数
    noBasicParams: !options.basicParams,
    // 新版本webview5.0参数
    basicParams: options.basicParams,
    // 老版本webview4.0参数
    header: httpHead,
    // 新版本webview5.0参数
    httpHeader: httpHead
  };
  const opt2: Record<string, any> = {
    url: _url,
    headers: httpHead,
    needEncrypted: false
  };

  if (initSign) {
    opt1.sign = initSign;
    opt2.signKey = initSign;
  }

  if (method === MethodEnum.GET) {
    opt1.cache = options.cache || false;
    opt2.needCache = options.cache || false;
    opt2.url += '&' + Utils.param(_params);
  } else {
    if (hasNewFeature) {
      if (typeof params === 'string') {
        if (params.match(/=/gi)) {
          opt2.postForm = Utils.split(params, '&', '=', true);
        } else {
          opt2.postString = params;
        }
      } else {
        opt2.postForm = params;
      }
    }
  }

  const http = hasNewFeature ? HTTP2 : HTTP;
  const opt = hasNewFeature ? opt2 : opt1;
  const fn = method === MethodEnum.GET ? http.get : http.post;

  return new Promise((resolve, reject) => {
    fn({
      ...opt,
      callback(data: MCProtocolDataModel) {
        // console.log(data);
        if (data.success) {
          resolve(data.data);
        } else {
          reject(data);
        }
      }
    });
  });
};
