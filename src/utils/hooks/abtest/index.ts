import { reactive } from 'vue';
import Utils from '@/utils/utils';
import { TypeEnum } from '@/components/dialog-result/constants';
import {
  AbtestResponse,
  AccessFineLocationModel,
  MotoModel,
  NewUserModel,
  ReconfirmModel,
  StateModel
} from '@/utils/hooks/abtest/types';
import { AbtestEnum, DEFAULT_GUIDE_LOC, PullSuccessEnum } from '@/utils/hooks/abtest/constants';
import MCProtocol from '@simplex/simple-mcprotocol';
import { PostAbtestStore } from '@/store/awd/home-lead-advertise';
import { FetchAbtestStore } from '@/store/swallow/config';

// 远程配置
export const useRemoteSettingStore = () => {
  const state = reactive<StateModel>({
    // 二次确认
    reconfirm: false,
    reconfirmInfo: null,
    // 挽留弹窗
    newUserImg: null,
    newUserUrl: null,
    // 是否禁止一键登录
    isBanLogin: false,
    // 问卷页面提示是否展示
    questionnaireTipVisible: false
  });

  const methods = {
    // 二次确认
    async getReconfirmDialog(): Promise<PullSuccessEnum> {
      const cityCode = (await Utils.getSystemBaseInfo())._userCity;

      return new Promise(resolve => {
        FetchAbtestStore({
          cityCode,
          key: AbtestEnum.RECONFIRM_DIALOD,
          typeCode: 1
        })
          .then((data: ReconfirmModel) => {
            state.reconfirm = !!data;
            state.reconfirmInfo = data;
            resolve(!!data ? PullSuccessEnum.SUCCESS : PullSuccessEnum.ERROR);
          })
          .catch(() => {
            state.reconfirm = false;
            resolve(PullSuccessEnum.DEFAULT);
          });
      });
    },
    // 挽留弹窗
    getNewUserDialog(userAreaCode: string, type: TypeEnum): Promise<boolean> {
      // userAreaCode 取的表单code，如果表单没有值则取基础参数

      return new Promise(resolve => {
        FetchAbtestStore({
          key: AbtestEnum.NEW_USER_DIALOG,
          cityCode: userAreaCode,
          typeCode: 1
        })
          .then((data: NewUserModel) => {
            if (!data) {
              resolve(false);
              return;
            }

            if (type === TypeEnum.SUBMIT) {
              state.newUserImg = data.submit_img;
              state.newUserUrl = data.submit_url;
            } else {
              state.newUserImg = data.cancel_img;
              state.newUserUrl = data.cancel_url;
            }

            resolve(true);
          })
          .catch(() => {
            resolve(false);
          });
      });
    },
    // 驾照类型弹窗，是否摩托车
    getMotoDialog(cityCode: string): Promise<boolean> {
      return new Promise(resolve => {
        PostAbtestStore({
          cityCode,
          // cityCode: '420111',
          key: AbtestEnum.MOTO_GUIDE
        })
          .then((data: AbtestResponse<MotoModel>) => {
            const show = data.params?.code?.includes(cityCode.slice(0, 4));
            console.log('moto show: ', show);

            resolve(show);
          })
          .catch(() => {
            resolve(false);
          });
      });
    },
    // 是否禁止一键登录
    async getDisabledQuicklyLogin(): Promise<PullSuccessEnum> {
      const cityCode = (await Utils.getSystemBaseInfo())._userCity;

      return new Promise(resolve => {
        FetchAbtestStore({
          cityCode,
          key: AbtestEnum.QUICKLY_LOGIN_FORBIDEN,
          typeCode: 1
        })
          .then((data: boolean) => {
            state.isBanLogin = !!data;
            resolve(!!data ? PullSuccessEnum.SUCCESS : PullSuccessEnum.ERROR);
          })
          .catch(() => {
            resolve(PullSuccessEnum.DEFAULT);
          });
      });
    },
    // 是否展示推荐弹窗
    getRecommendDialog(cityCode: string): Promise<boolean> {
      return new Promise(resolve => {
        FetchAbtestStore({
          cityCode,
          key: AbtestEnum.NEW_USER_1V1,
          typeCode: 1
        })
          .then((data: boolean) => {
            resolve(!!data);
          })
          .catch(() => {
            resolve(false);
          });
      });
    },
    // 问卷页面提示是否展示
    async getQuestionnaireTipShow(): Promise<void> {
      const cityCode = (await Utils.getSystemBaseInfo())._userCity;

      return new Promise(resolve => {
        FetchAbtestStore({
          cityCode,
          key: AbtestEnum.INFORMATION_COLLECT_CANCEL_TIP,
          typeCode: 1
        })
          .then((data: boolean) => {
            state.questionnaireTipVisible = !!data;
            resolve();
          })
          .catch(() => {
            state.questionnaireTipVisible = false;
            resolve();
          });
      });
    },
    // 获取定位策略
    getLocationStrategyKey(): Promise<AccessFineLocationModel> {
      console.log('getAbTestConfig start');
      return new Promise((resolve, reject) => {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        MCProtocol['jiakao-global'].getAbTestConfig({
          callback: res => {
            console.log('getAbTestConfig: ', res);
            if (res.success) {
              res.data[AbtestEnum.JK_GUIDE_LOC]
                ? resolve(res.data[AbtestEnum.JK_GUIDE_LOC])
                : resolve(DEFAULT_GUIDE_LOC);
            } else {
              reject();
            }
          }
        });
      });
    }
  };

  return {
    state,
    ...methods
  };
};
