/**
 * Vue 3 TypeScript 类型声明文件
 * 解决 Vue 文件模块识别问题
 */

declare module '*.vue' {
  import type { DefineComponent } from 'vue';
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

// 扩展 Vue 运行时核心类型
declare module '@vue/runtime-core' {
  export interface ComponentCustomProperties {
    // 全局属性类型声明可以在这里添加
    // 例如：$router, $store 等
  }
  
  export interface GlobalComponents {
    // 全局组件类型声明
    // 这里的声明会与 components.d.ts 合并
  }
}

// 确保这个文件被视为模块
export {};
