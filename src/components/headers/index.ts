import MCProtocol from '@simplex/simple-mcprotocol';
import { defineComponent, onMounted, reactive, toRefs } from 'vue';
// import Utils from '@/utils/utils';
import { StateModel, EmitModel } from './types';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  setup(props, { emit }: { emit: EmitModel }) {
    const state = reactive(<StateModel>{
      top: 47 // 顶部padding，默认iphone13
    });
    const methods = {
      // 初始化
      init() {
        // 隐藏原header，本地保留
        // if (!Utils.isLocal) {
        MCProtocol.Core.Web.setting({
          titleBar: false,
          title: ''
        });
        // }

        // 设置padding
        MCProtocol.Core.System.env(res => {
          state.top = res.data.statusBarHeight;
        });
      },
      // 右上角返回
      onBack() {
        // 首页和问卷页有后置事件
        if (['帮我找驾校', '问卷调查'].includes(props.title)) {
          emit('onBack');
        } else {
          MCProtocol.Core.Web.close();
        }
      }
    };

    onMounted(() => {
      methods.init();
    });

    return {
      ...methods,
      ...toRefs(state)
    };
  }
});
