import { request } from '@/utils/store';
import { MethodEnum } from '@/utils/store/type';

// 分步留资
export const PostAddPartStore = (params, baseParams = '') =>
  request({
    url: `${APP.domain.lead}/api/open/lead/add-part.htm?` + baseParams,
    method: MethodEnum.POST,
    params,
    options: {
      sign: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9'
    }
  });

// 机动车留资
export const PostAdd2Store = (params, baseParams = '') =>
  request({
    url: `${APP.domain.lead}/api/open/lead/add.htm?` + baseParams,
    method: MethodEnum.POST,
    params,
    options: {
      sign: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9'
    }
  });

// 无人机留资
export const PostAddUavStore = (params, baseParams = '') =>
  request({
    url: `${APP.domain.lead}/api/open/lead/uav-add.htm?` + baseParams,
    method: MethodEnum.POST,
    params,
    options: {
      sign: '*#06#a4qjhYuLjpVHjXZtjKOJh4Y9'
    }
  });
