// 页面跳转参数
export interface JumpQueryModel {
  // 路径
  path: string;
  // webview容器参数
  coreOpenParams?: CoreOpenParamsModel;
  // web 跳转参数
  webQuery?: Record<string, any>;
  // web 跳转类型
  type?: 'href' | 'replace';
}

// webview容器参数
export interface CoreOpenParamsModel {
  /** 打开的网页地址 */
  url?: string;
  /** 标题。优先级低于网页自身标题 */
  title?: string;
  /**
   * 标题栏是否展示  boolean 默认为true。
   * 当需要设置网页全屏时，iOS 和 安卓 设置 titleBar false。同时安卓另外还需要设置 noTopInset 为 true
   */
  titleBar?: boolean;
  /** 初始化网页加载时是否需要app loading样式。默认true不需要 */
  async?: boolean;
  /** 标题栏右边的菜单栏（三个点）是否展示 */
  menu?: boolean;
  /** 标题栏右边的菜单栏里的菜单项 '复制, 刷新' 'copy', 'refresh' */
  button?: string;
  /** 请求头 json */
  httpHeader?: string;
  /** landscape 横屏, portrait 竖屏, auto 自动。iOS 此参数 2022年08月 后版本开始支持。如无横屏需求，请不要填写 orientation 参数。 */
  orientation?: 'landscape' | 'portrait' | 'auto';
  /** 状态栏主题: light, dard。默认 dark 黑色图表和字体，light 为白色。iOS 和 安卓 此参数 2022年08月 后版本开始支持 */
  statusBarTheme?: 'light' | 'dark';
  /** 是否沉浸式状态栏(安卓特有参数。如需设置全屏，安卓必须设置次参数) 2022年08月 后版本开始支持 */
  noTopInset?: boolean;
  /** 打开新webview是否关闭当前webview (0不关闭，1关闭)。默认0不关闭 */
  closeCurrent?: 0 | 1;
  /** 安卓独有参数 */
  supportAdjustSpanWhenNoTopInset?: boolean;

  [key: string]: any;
}

// 打点参数
export interface StatPropsModel {
  /** 页面名称 */
  PageName: string;
  /** 打点group */
  eventId?: string;
  /** 片段1名称 */
  fragmentName1?: string;
  /** 片段2名称 */
  fragmentName2?: string;
  /** 动作类型 */
  actionType: string;
  /** 动作名称 */
  actionName?: string;
  numbers?: object;

  /** 其他参数 */
  [k: string]: unknown;
}

// 获取系统基本信息
export interface SystemInfoModel {
  data: {
    _userCity: string;
    _longitude?: number;
    _latitude?: number;
    _systemVersion: number;
    authToken: string;

    _gpsCity: string;
    _ipCipy?: string;
  };
}
